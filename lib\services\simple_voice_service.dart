import 'dart:convert';
import 'dart:developer';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';

class VoiceWebSocketService {
  static const String _wsUrl =
      'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01';
  static WebSocketChannel? _channel;
  static bool _isConnected = false;
  static Function(String)? onTextResponse;
  static Function(String)? onAudioResponse;
  static Function(String)? onError;

  // الاتصال بـ WebSocket للمحادثة الصوتية المباشرة
  static Future<WebSocketChannel?> connect() async {
    try {
      log("🎤 Connecting to OpenAI Realtime API...");

      // التحقق من وجود مفتاح API
      final apiKey = dotenv.env['OPENAI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        log("❌ OpenAI API key not found");
        return null;
      }

      _channel = IOWebSocketChannel.connect(
        _wsUrl,
        headers: {
          'Authorization': 'Bearer $apiKey',
          'OpenAI-Beta': 'realtime=v1',
        },
      );

      // إرسال إعدادات الجلسة
      final sessionConfig = {
        'type': 'session.update',
        'session': {
          'modalities': ['text', 'audio'],
          'instructions':
              """أنت وفادة، مساعدة صوتية ودليل سياحي للمدينة المنورة.

🎤 في المحادثة الصوتية:
- ابدئي بالسلام والترحيب في أول مرة فقط
- كوني مفيدة وودودة
- اجعلي إجاباتك مناسبة للاستماع (ليس طويلة جداً)

📍 تخصصك:
- تاريخ المدينة المنورة
- قصص الصحابة والسيرة النبوية
- فضائل المدينة وبركاتها
- المعالم المقدسة والمساجد
- نصائح عملية للزوار

🎯 أسلوبك الصوتي:
- كلام واضح ومفهوم
- توقفات مناسبة
- نبرة ودودة ومرحبة""",
          'voice': 'alloy',
          'input_audio_format': 'pcm16',
          'output_audio_format': 'pcm16',
          'input_audio_transcription': {'model': 'whisper-1'},
          'turn_detection': {
            'type': 'server_vad',
            'threshold': 0.5,
            'prefix_padding_ms': 300,
            'silence_duration_ms': 500
          },
          'tools': [],
          'tool_choice': 'auto',
          'temperature': 0.8,
          'max_response_output_tokens': 4096
        }
      };

      _channel!.sink.add(jsonEncode(sessionConfig));
      _isConnected = true;
      log("✅ Voice chat session configured");

      // إعداد معالج الردود
      _setupResponseHandler();

      return _channel;
    } catch (e) {
      log("❌ Voice chat connection error: $e");
      _isConnected = false;
      return null;
    }
  }

  // إعداد معالج الردود
  static void _setupResponseHandler() {
    if (_channel != null) {
      _channel!.stream.listen(
        (data) {
          try {
            final response = jsonDecode(data);
            _handleResponse(response);
          } catch (e) {
            log("❌ Error parsing response: $e");
            onError?.call("خطأ في معالجة الرد: $e");
          }
        },
        onError: (error) {
          log("❌ WebSocket stream error: $error");
          onError?.call("خطأ في الاتصال: $error");
          _isConnected = false;
        },
        onDone: () {
          log("🔴 WebSocket connection closed");
          _isConnected = false;
        },
      );
    }
  }

  // معالجة الردود من OpenAI
  static void _handleResponse(Map<String, dynamic> response) {
    final type = response['type'];

    switch (type) {
      case 'session.created':
        log("✅ Session created successfully");
        break;
      case 'session.updated':
        log("✅ Session updated successfully");
        break;
      case 'response.text.delta':
        final delta = response['delta'] ?? '';
        onTextResponse?.call(delta);
        break;
      case 'response.text.done':
        log("✅ Text response completed");
        break;
      case 'response.audio.delta':
        final audio = response['delta'] ?? '';
        onAudioResponse?.call(audio);
        break;
      case 'response.audio.done':
        log("✅ Audio response completed");
        break;
      case 'error':
        final errorMsg = response['error']?['message'] ?? 'خطأ غير معروف';
        log("❌ OpenAI error: $errorMsg");
        onError?.call(errorMsg);
        break;
      case 'input_audio_buffer.speech_started':
        log("🎤 Speech started");
        break;
      case 'input_audio_buffer.speech_stopped':
        log("🎤 Speech stopped");
        break;
      default:
        log("📝 Received: $type");
    }
  }

  // إرسال رسالة نصية عبر WebSocket
  static void sendTextMessage(String message) {
    if (_channel != null && _isConnected) {
      try {
        log("📤 Sending message: $message");

        final messageData = {
          'type': 'conversation.item.create',
          'item': {
            'type': 'message',
            'role': 'user',
            'content': [
              {'type': 'input_text', 'text': message}
            ]
          }
        };

        _channel!.sink.add(jsonEncode(messageData));

        // طلب الرد
        final responseRequest = {
          'type': 'response.create',
          'response': {
            'modalities': ['text', 'audio'],
            'instructions':
                'أجيبي على سؤال المستخدم بشكل مفيد ومناسب للمحادثة الصوتية. اجعلي الرد واضح ومختصر.'
          }
        };

        _channel!.sink.add(jsonEncode(responseRequest));
        log("✅ Text message sent successfully");
      } catch (e) {
        log("❌ Error sending text message: $e");
        onError?.call("خطأ في إرسال الرسالة: $e");
      }
    } else {
      log("❌ WebSocket not connected");
      onError?.call("الاتصال غير متوفر. حاول الاتصال أولاً.");
    }
  }

  // إرسال بيانات صوتية
  static void sendAudioData(List<int> audioData) {
    if (_channel != null && _isConnected) {
      try {
        final audioMessage = {
          'type': 'input_audio_buffer.append',
          'audio': base64Encode(audioData)
        };

        _channel!.sink.add(jsonEncode(audioMessage));
        log("🎤 Audio data sent");
      } catch (e) {
        log("❌ Error sending audio data: $e");
      }
    }
  }

  // إنهاء إدخال الصوت وطلب الرد
  static void commitAudio() {
    if (_channel != null && _isConnected) {
      try {
        final commitMessage = {'type': 'input_audio_buffer.commit'};

        _channel!.sink.add(jsonEncode(commitMessage));

        // طلب الرد
        final responseRequest = {
          'type': 'response.create',
          'response': {
            'modalities': ['text', 'audio']
          }
        };

        _channel!.sink.add(jsonEncode(responseRequest));
        log("🎤 Audio committed and response requested");
      } catch (e) {
        log("❌ Error committing audio: $e");
      }
    }
  }

  // إغلاق الاتصال
  static void disconnect() {
    try {
      _channel?.sink.close();
      _channel = null;
      _isConnected = false;
      log("🔴 WebSocket disconnected");
    } catch (e) {
      log("❌ Error disconnecting: $e");
    }
  }

  // التحقق من حالة الاتصال
  static bool get isConnected => _isConnected;

  // الحصول على قناة WebSocket للاستماع للردود
  static WebSocketChannel? get channel => _channel;
}
