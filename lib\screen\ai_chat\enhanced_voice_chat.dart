import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:iconsax/iconsax.dart';
import 'dart:math' as math;
import 'dart:developer';
import '../../services/simple_voice_service.dart';
import '../../services/voice_test_service.dart';

class EnhancedVoiceChatScreen extends StatefulWidget {
  const EnhancedVoiceChatScreen({super.key});

  @override
  State<EnhancedVoiceChatScreen> createState() =>
      _EnhancedVoiceChatScreenState();
}

// Voice Quick Action Model
class VoiceQuickAction {
  final String title;
  final IconData icon;
  final Color color;
  final String prompt;
  final String description;

  VoiceQuickAction({
    required this.title,
    required this.icon,
    required this.color,
    required this.prompt,
    required this.description,
  });
}

class _EnhancedVoiceChatScreenState extends State<EnhancedVoiceChatScreen>
    with TickerProviderStateMixin {
  bool _isListening = false;
  bool _isConnected = false;
  String _status = 'اضغط للبدء';
  String _currentMessage = '';
  final double _voiceLevel = 0.0;

  // Simple voice service for speech recognition and TTS
  final bool _isVoiceInitialized = false;

  // Voice Quick Actions - مخصصة للمحادثة الصوتية
  final List<VoiceQuickAction> _voiceQuickActions = [
    VoiceQuickAction(
      title: 'تاريخ المدينة',
      icon: Iconsax.book,
      color: const Color(0xFFD4AF37), // ذهبي
      prompt: 'حدثني عن تاريخ المدينة المنورة في عهد الرسول صلى الله عليه وسلم',
      description: 'قصص وأحداث تاريخية',
    ),
    VoiceQuickAction(
      title: 'نصائح للزوار',
      icon: Iconsax.lamp_charge,
      color: const Color(0xFF20B2AA), // تركوازي
      prompt: 'أعطني نصائح مهمة وعملية لزيارة المدينة المنورة',
      description: 'إرشادات عملية مفيدة',
    ),
    VoiceQuickAction(
      title: 'قصص الصحابة',
      icon: Iconsax.crown,
      color: const Color(0xFF9370DB), // بنفسجي
      prompt: 'اروي لي قصة من قصص الصحابة في المدينة المنورة',
      description: 'حكايات الصحابة الكرام',
    ),
    VoiceQuickAction(
      title: 'فضائل المدينة',
      icon: Iconsax.star,
      color: const Color(0xFFFF6B6B), // أحمر فاتح
      prompt: 'حدثني عن فضائل المدينة المنورة وبركاتها',
      description: 'الفضائل والبركات',
    ),
    VoiceQuickAction(
      title: 'معالم مقدسة',
      icon: Iconsax.building_4,
      color: const Color(0xFF4ECDC4), // أخضر مائي
      prompt: 'اشرح لي عن المعالم المقدسة في المدينة المنورة',
      description: 'المساجد والأماكن المقدسة',
    ),
  ];

  // Animation Controllers
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late AnimationController _breathingController;
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late AnimationController _rippleController;

  // Animations
  late Animation<double> _pulseAnimation;
  late Animation<double> _breathingAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Pulse animation for main button
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Wave animation for voice visualization
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Breathing animation for idle state
    _breathingController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    _breathingAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _breathingController, curve: Curves.easeInOut),
    );

    // Rotation animation for loading
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    // Scale animation for interactions
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );

    // Ripple animation for voice waves
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // Start breathing animation
    _breathingController.repeat(reverse: true);
    _waveController.repeat();
  }

  @override
  void dispose() {
    // إيقاف المحادثة الصوتية
    _stopListening();

    // تنظيف الأنيميشن
    _pulseController.dispose();
    _waveController.dispose();
    _breathingController.dispose();
    _rotationController.dispose();
    _scaleController.dispose();
    _rippleController.dispose();

    super.dispose();
  }

  void _toggleVoiceChat() async {
    if (_isListening) {
      _stopListening();
    } else {
      await _startListening();
    }
  }

  Future<void> _startListening() async {
    try {
      setState(() {
        _status = 'جاري الاتصال...';
      });

      // إعداد معالجات الردود
      VoiceWebSocketService.onTextResponse = (text) {
        setState(() {
          _currentMessage += text;
        });
      };

      VoiceWebSocketService.onAudioResponse = (audio) {
        log('🔊 Received audio data');
        // يمكن إضافة تشغيل الصوت هنا لاحقاً
      };

      VoiceWebSocketService.onError = (error) {
        setState(() {
          _status = 'خطأ: $error';
        });
        log('❌ Voice service error: $error');
      };

      // الاتصال بـ WebSocket
      final channel = await VoiceWebSocketService.connect();

      if (channel == null) {
        setState(() {
          _status = 'فشل في الاتصال. حاول مرة أخرى';
        });
        return;
      }

      setState(() {
        _isListening = true;
        _isConnected = true;
        _status = 'متصل - يمكنك التحدث الآن';
      });

      _pulseController.repeat(reverse: true);
      _rippleController.repeat();

      log('✅ Voice WebSocket connected successfully');
    } catch (e) {
      log('❌ Error starting voice chat: $e');
      setState(() {
        _status = 'حدث خطأ في الاتصال: $e';
      });
    }
  }

  void _stopListening() {
    setState(() {
      _isListening = false;
      _isConnected = false;
      _status = 'اضغط للبدء';
    });

    _pulseController.stop();
    _rippleController.stop();

    // تنظيف معالجات الردود
    VoiceWebSocketService.onTextResponse = null;
    VoiceWebSocketService.onAudioResponse = null;
    VoiceWebSocketService.onError = null;

    // إغلاق WebSocket
    VoiceWebSocketService.disconnect();
    log('🔴 Voice WebSocket disconnected');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(),
      body: Container(
        decoration: _buildBackgroundDecoration(),
        child: SafeArea(
          child: Column(
            children: [
              const SizedBox(height: 20),
              _buildStatusSection(),
              const SizedBox(height: 20),
              // Voice Quick Actions
              _buildVoiceQuickActions(),
              const SizedBox(height: 30),
              Expanded(
                child: _buildVoiceVisualization(),
              ),
              _buildControlsSection(),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Iconsax.arrow_left_2,
            color: Colors.white,
            size: 20,
          ),
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        'المحادثة الصوتية',
        style: GoogleFonts.ibmPlexSansArabic(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Iconsax.flash,
              color: Colors.white,
              size: 20,
            ),
          ),
          onPressed: _testConnection,
        ),
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Iconsax.setting_2,
              color: Colors.white,
              size: 20,
            ),
          ),
          onPressed: () {
            // Settings
          },
        ),
      ],
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          const Color(0xFF4F908E),
          const Color(0xFF6BA3A0),
          const Color(0xFF4F908E).withValues(alpha: 0.8),
        ],
        stops: const [0.0, 0.5, 1.0],
      ),
    );
  }

  Widget _buildStatusSection() {
    return Column(
      children: [
        AnimatedBuilder(
          animation: _breathingAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _breathingAnimation.value,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _isListening ? Colors.green : Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _status,
                      style: GoogleFonts.ibmPlexSansArabic(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        if (_currentMessage.isNotEmpty) ...[
          const SizedBox(height: 20),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              _currentMessage,
              style: GoogleFonts.ibmPlexSansArabic(
                color: Colors.white,
                fontSize: 14,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ],
    );
  }

  // Voice Quick Actions - تصميم مختلف وجميل للمحادثة الصوتية
  Widget _buildVoiceQuickActions() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        itemCount: _voiceQuickActions.length,
        itemBuilder: (context, index) {
          final action = _voiceQuickActions[index];
          return _buildVoiceActionCard(action, index);
        },
      ),
    );
  }

  Widget _buildVoiceActionCard(VoiceQuickAction action, int index) {
    return AnimatedBuilder(
      animation: _waveController,
      builder: (context, child) {
        final delay = index * 0.2;
        final animationValue = (_waveController.value + delay) % 1.0;

        return Container(
          width: 100,
          margin: const EdgeInsets.only(right: 12),
          child: Transform.scale(
            scale: 1.0 + (math.sin(animationValue * 2 * math.pi) * 0.02),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () => _handleVoiceAction(action),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        action.color.withValues(alpha: 0.3),
                        action.color.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: action.color.withValues(alpha: 0.5),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: action.color.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: action.color.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          action.icon,
                          color: action.color,
                          size: 24,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        action.title,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        action.description,
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 8,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleVoiceAction(VoiceQuickAction action) async {
    try {
      // بدء الاتصال إذا لم يكن متصل
      if (!_isConnected) {
        await _startListening();
        // انتظار قليل للتأكد من الاتصال
        await Future.delayed(const Duration(seconds: 1));
      }

      setState(() {
        _status = 'جاري إرسال ${action.title}...';
        _currentMessage = ''; // تنظيف الرسالة السابقة
      });

      log('🎤 Voice action: ${action.title}');
      log('📝 Prompt: ${action.prompt}');

      // إرسال النص عبر WebSocket
      VoiceWebSocketService.sendTextMessage(action.prompt);

      setState(() {
        _status = 'تم الإرسال - انتظر الرد...';
      });
    } catch (e) {
      log('❌ Error in voice action: $e');
      setState(() {
        _status = 'حدث خطأ. حاول مرة أخرى';
        _currentMessage = 'عذراً، حدث خطأ في معالجة طلبك';
      });
    }
  }

  Widget _buildVoiceVisualization() {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer ripple effects
          if (_isListening) ..._buildRippleEffects(),

          // Voice wave visualization
          if (_isListening) _buildVoiceWaves(),

          // Main voice button
          _buildMainVoiceButton(),

          // Floating particles
          if (_isListening) ..._buildFloatingParticles(),
        ],
      ),
    );
  }

  List<Widget> _buildRippleEffects() {
    return List.generate(3, (index) {
      return AnimatedBuilder(
        animation: _rippleController,
        builder: (context, child) {
          final delay = index * 0.3;
          final animationValue = (_rippleController.value + delay) % 1.0;

          return Container(
            width: 200 + (animationValue * 100),
            height: 200 + (animationValue * 100),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white
                    .withValues(alpha: 0.3 - (animationValue * 0.3)),
                width: 2,
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildVoiceWaves() {
    return SizedBox(
      width: 300,
      height: 300,
      child: AnimatedBuilder(
        animation: _waveController,
        builder: (context, child) {
          return CustomPaint(
            painter: VoiceWavePainter(
              animationValue: _waveController.value,
              voiceLevel: _voiceLevel,
            ),
            size: const Size(300, 300),
          );
        },
      ),
    );
  }

  Widget _buildMainVoiceButton() {
    return AnimatedBuilder(
      animation: _isListening ? _pulseAnimation : _breathingAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale:
              _isListening ? _pulseAnimation.value : _breathingAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _scaleController.forward(),
            onTapUp: (_) => _scaleController.reverse(),
            onTapCancel: () => _scaleController.reverse(),
            onTap: _toggleVoiceChat,
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: _isListening
                            ? [
                                const Color(0xFF00E676),
                                const Color(0xFF00C853),
                              ]
                            : [
                                Colors.white,
                                Colors.white.withValues(alpha: 0.9),
                              ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: _isListening
                              ? const Color(0xFF00E676).withValues(alpha: 0.4)
                              : Colors.black.withValues(alpha: 0.2),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: Icon(
                      _isListening ? Iconsax.microphone_2 : Iconsax.microphone,
                      size: 50,
                      color:
                          _isListening ? Colors.white : const Color(0xFF4F908E),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildFloatingParticles() {
    return List.generate(8, (index) {
      return AnimatedBuilder(
        animation: _waveController,
        builder: (context, child) {
          final angle =
              (index * math.pi * 2 / 8) + (_waveController.value * math.pi * 2);
          final radius =
              80 + (math.sin(_waveController.value * math.pi * 2) * 20);
          final x = math.cos(angle) * radius;
          final y = math.sin(angle) * radius;

          return Transform.translate(
            offset: Offset(x, y),
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.6),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.3),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildControlsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildControlButton(
            icon: Iconsax.volume_high,
            label: 'مكبر الصوت',
            onTap: () {
              // Toggle speaker
            },
          ),
          _buildControlButton(
            icon: Iconsax.pause,
            label: 'إيقاف مؤقت',
            onTap: () {
              // Pause conversation
            },
          ),
          _buildControlButton(
            icon: Iconsax.call_slash,
            label: 'إنهاء',
            onTap: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.ibmPlexSansArabic(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

// Custom painter for voice wave visualization
class VoiceWavePainter extends CustomPainter {
  final double animationValue;
  final double voiceLevel;

  VoiceWavePainter({
    required this.animationValue,
    required this.voiceLevel,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.3)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final baseRadius = size.width / 4;

    // Draw multiple wave circles
    for (int i = 0; i < 5; i++) {
      final waveOffset = (animationValue + (i * 0.2)) % 1.0;
      final radius = baseRadius + (waveOffset * 50) + (voiceLevel * 20);
      final alpha = 0.5 - (waveOffset * 0.5);

      paint.color = Colors.white.withValues(alpha: alpha);
      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
