import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';
import 'dart:async';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import 'package:just_audio/just_audio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';

/// خدمة المحادثة الصوتية المباشرة مع OpenAI Realtime API
/// تدعم المحادثة الصوتية المباشرة مثل المكالمة الهاتفية
class RealtimeVoiceService {
  static const String _wsUrl =
      'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01';

  static WebSocketChannel? _channel;
  static bool _isConnected = false;
  static bool _voiceModeEnabled = false;

  // Callbacks للردود
  static Function(String)? onTextResponse;
  static Function(String)? onAudioResponse;
  static Function(String)? onError;
  static Function()? onAudioPlaybackComplete;
  static Function()? onVoiceModeEnabled;
  static Function()? onVoiceModeDisabled;

  // مشغل الصوت
  static AudioPlayer? _audioPlayer;
  static final List<int> _audioBuffer = [];

  // تسجيل الصوت المباشر
  static final AudioRecorder _audioRecorder = AudioRecorder();
  static bool _isRecording = false;
  static StreamSubscription<Uint8List>? _audioStreamSubscription;

  /// الاتصال بـ OpenAI Realtime API
  static Future<WebSocketChannel?> connect() async {
    try {
      log("🎤 Connecting to OpenAI Realtime API...");

      // التحقق من وجود مفتاح API
      final apiKey = dotenv.env['OPENAI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        log("❌ OpenAI API key not found");
        onError?.call("مفتاح OpenAI API غير موجود");
        return null;
      }

      _channel = IOWebSocketChannel.connect(
        _wsUrl,
        headers: {
          'Authorization': 'Bearer $apiKey',
          'OpenAI-Beta': 'realtime=v1',
        },
      );

      // إرسال إعدادات الجلسة للمحادثة الصوتية
      final sessionConfig = {
        'type': 'session.update',
        'session': {
          'modalities': ['text', 'audio'],
          'instructions':
              '''أنت وفادة، مساعدة صوتية ودليل سياحي للمدينة المنورة.

🎤 في المحادثة الصوتية المباشرة:
- استمع للمستخدم بعناية
- أجب بشكل طبيعي وودود
- اجعل إجاباتك مناسبة للاستماع
- تفاعل بشكل مباشر مثل المكالمة الهاتفية

📍 تخصصك:
- تاريخ المدينة المنورة وقصصها
- معالم المدينة المقدسة
- نصائح عملية للزوار
- فضائل المدينة وبركاتها''',
          'voice': 'alloy',
          'input_audio_format': 'pcm16',
          'output_audio_format': 'pcm16',
          'input_audio_transcription': {'model': 'whisper-1'},
          'turn_detection': {
            'type': 'server_vad',
            'threshold': 0.5,
            'prefix_padding_ms': 300,
            'silence_duration_ms': 1000
          },
          'temperature': 0.8,
          'max_response_output_tokens': 4096
        }
      };

      _channel!.sink.add(jsonEncode(sessionConfig));
      _isConnected = true;
      log("✅ Realtime API connected successfully");

      // إعداد معالج الردود
      _setupResponseHandler();

      return _channel;
    } catch (e) {
      log("❌ Connection error: $e");
      onError?.call("خطأ في الاتصال: $e");
      _isConnected = false;
      return null;
    }
  }

  /// إعداد معالج الردود من OpenAI
  static void _setupResponseHandler() {
    if (_channel != null) {
      _channel!.stream.listen(
        (data) {
          try {
            final response = jsonDecode(data);
            _handleResponse(response);
          } catch (e) {
            log("❌ Error parsing response: $e");
            onError?.call("خطأ في معالجة الرد: $e");
          }
        },
        onError: (error) {
          log("❌ WebSocket stream error: $error");
          onError?.call("خطأ في الاتصال: $error");
          _isConnected = false;
        },
        onDone: () {
          log("🔴 WebSocket connection closed");
          _isConnected = false;
          _voiceModeEnabled = false;
        },
      );
    }
  }

  /// معالجة الردود من OpenAI
  static void _handleResponse(Map<String, dynamic> response) {
    final type = response['type'];

    switch (type) {
      case 'session.created':
        log("✅ Session created successfully");
        break;
      case 'session.updated':
        log("✅ Session updated successfully");
        break;
      case 'response.text.delta':
        final delta = response['delta'] ?? '';
        onTextResponse?.call(delta);
        break;
      case 'response.text.done':
        log("✅ Text response completed");
        break;
      case 'response.audio.delta':
        final audio = response['delta'] ?? '';
        if (audio.isNotEmpty) {
          _handleAudioDelta(audio);
        }
        onAudioResponse?.call(audio);
        break;
      case 'response.audio.done':
        log("✅ Audio response completed");
        _playAudioBuffer();
        break;
      case 'input_audio_buffer.speech_started':
        log("🎤 User started speaking");
        break;
      case 'input_audio_buffer.speech_stopped':
        log("🎤 User stopped speaking");
        // طلب الرد من OpenAI عند توقف المستخدم عن الكلام
        _requestResponse();
        break;
      case 'response.created':
        log("✅ Response created");
        break;
      case 'response.done':
        log("✅ Response completed");
        break;
      case 'error':
        final errorMsg = response['error']?['message'] ?? 'خطأ غير معروف';
        log("❌ OpenAI error: $errorMsg");
        onError?.call(errorMsg);
        break;
      default:
        log("📝 Received event: $type");
    }
  }

  /// تفعيل المحادثة الصوتية المباشرة
  static Future<bool> enableVoiceMode() async {
    if (_channel == null || !_isConnected) {
      log("❌ WebSocket not connected");
      onError?.call("الاتصال غير متوفر");
      return false;
    }

    try {
      // طلب صلاحية الميكروفون
      final permission = await Permission.microphone.request();
      if (permission != PermissionStatus.granted) {
        log("❌ Microphone permission denied");
        onError?.call("يرجى السماح بالوصول للميكروفون");
        return false;
      }

      // التحقق من توفر التسجيل
      if (!await _audioRecorder.hasPermission()) {
        log("❌ No recording permission");
        onError?.call("لا توجد صلاحية للتسجيل");
        return false;
      }

      // بدء التسجيل المباشر
      await _startRealTimeRecording();

      // تفعيل المحادثة الصوتية المباشرة
      _voiceModeEnabled = true;
      log("🎤 Voice mode enabled - يمكنك التحدث الآن");
      onVoiceModeEnabled?.call();

      return true;
    } catch (e) {
      log("❌ Error enabling voice mode: $e");
      onError?.call("خطأ في تفعيل المحادثة الصوتية: $e");
      return false;
    }
  }

  /// بدء التسجيل المباشر وإرسال الصوت إلى OpenAI
  static Future<void> _startRealTimeRecording() async {
    try {
      // بدء التسجيل بصيغة PCM16 مناسبة لـ OpenAI
      final stream = await _audioRecorder.startStream(
        const RecordConfig(
          encoder: AudioEncoder.pcm16bits,
          sampleRate: 24000,
          numChannels: 1,
        ),
      );

      _isRecording = true;
      log("🎤 Real-time recording started");

      // الاستماع لبيانات الصوت وإرسالها مباشرة
      _audioStreamSubscription = stream.listen(
        (audioData) {
          _sendAudioToOpenAI(audioData);
        },
        onError: (error) {
          log("❌ Audio stream error: $error");
          onError?.call("خطأ في تسجيل الصوت: $error");
        },
        onDone: () {
          log("🎤 Audio stream ended");
        },
      );
    } catch (e) {
      log("❌ Error starting real-time recording: $e");
      onError?.call("خطأ في بدء التسجيل: $e");
    }
  }

  /// إرسال بيانات الصوت مباشرة إلى OpenAI
  static void _sendAudioToOpenAI(Uint8List audioData) {
    if (_channel != null && _isConnected && _voiceModeEnabled) {
      try {
        // تحويل البيانات إلى Base64 وإرسالها
        final audioBase64 = base64Encode(audioData);

        final audioMessage = {
          'type': 'input_audio_buffer.append',
          'audio': audioBase64
        };

        _channel!.sink.add(jsonEncode(audioMessage));
        log("🎤 Audio chunk sent: ${audioData.length} bytes");
      } catch (e) {
        log("❌ Error sending audio to OpenAI: $e");
      }
    }
  }

  /// طلب الرد من OpenAI
  static void _requestResponse() {
    if (_channel != null && _isConnected) {
      try {
        final responseRequest = {
          'type': 'response.create',
          'response': {
            'modalities': ['text', 'audio']
          }
        };

        _channel!.sink.add(jsonEncode(responseRequest));
        log("📤 Response requested from OpenAI");
      } catch (e) {
        log("❌ Error requesting response: $e");
      }
    }
  }

  /// إيقاف المحادثة الصوتية المباشرة
  static void disableVoiceMode() {
    if (_voiceModeEnabled) {
      // إيقاف التسجيل
      _stopRealTimeRecording();

      _voiceModeEnabled = false;
      log("🔇 Voice mode disabled");
      onVoiceModeDisabled?.call();
    }
  }

  /// إيقاف التسجيل المباشر
  static Future<void> _stopRealTimeRecording() async {
    try {
      if (_isRecording) {
        await _audioStreamSubscription?.cancel();
        await _audioRecorder.stop();
        _isRecording = false;
        log("🎤 Real-time recording stopped");
      }
    } catch (e) {
      log("❌ Error stopping recording: $e");
    }
  }

  /// إرسال رسالة نصية
  static void sendTextMessage(String message) {
    if (_channel != null && _isConnected) {
      try {
        log("📤 Sending text message: $message");

        final messageData = {
          'type': 'conversation.item.create',
          'item': {
            'type': 'message',
            'role': 'user',
            'content': [
              {'type': 'input_text', 'text': message}
            ]
          }
        };

        _channel!.sink.add(jsonEncode(messageData));

        // طلب الرد
        final responseRequest = {
          'type': 'response.create',
          'response': {
            'modalities': ['text', 'audio']
          }
        };

        _channel!.sink.add(jsonEncode(responseRequest));
        log("✅ Text message sent successfully");
      } catch (e) {
        log("❌ Error sending text message: $e");
        onError?.call("خطأ في إرسال الرسالة: $e");
      }
    } else {
      log("❌ WebSocket not connected");
      onError?.call("الاتصال غير متوفر");
    }
  }

  /// معالجة بيانات الصوت الواردة
  static void _handleAudioDelta(String audioBase64) {
    try {
      final audioBytes = base64Decode(audioBase64);
      _audioBuffer.addAll(audioBytes);
      log("🔊 Audio delta received: ${audioBytes.length} bytes");
    } catch (e) {
      log("❌ Error handling audio delta: $e");
    }
  }

  /// تشغيل الصوت المجمع
  static Future<void> _playAudioBuffer() async {
    if (_audioBuffer.isEmpty) {
      log("⚠️ No audio data to play");
      return;
    }

    try {
      log("🎵 Playing audio buffer: ${_audioBuffer.length} bytes");

      // إنشاء مشغل صوت جديد إذا لم يكن موجود
      _audioPlayer ??= AudioPlayer();

      // تحويل PCM16 إلى WAV
      final wavData = _createWavFromPcm(_audioBuffer);

      // تشغيل الصوت من الذاكرة
      await _audioPlayer!.setAudioSource(
        AudioSource.uri(
          Uri.dataFromBytes(
            wavData,
            mimeType: 'audio/wav',
          ),
        ),
      );

      await _audioPlayer!.play();
      log("✅ Audio playback started");

      // الاستماع لانتهاء التشغيل
      _audioPlayer!.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.completed) {
          log("✅ Audio playback completed");
          onAudioPlaybackComplete?.call();
        }
      });

      // تنظيف البافر
      _audioBuffer.clear();
    } catch (e) {
      log("❌ Error playing audio: $e");
      _audioBuffer.clear();
    }
  }

  /// تحويل PCM16 إلى WAV
  static Uint8List _createWavFromPcm(List<int> pcmData) {
    const int sampleRate = 24000;
    const int channels = 1;
    const int bitsPerSample = 16;

    final int dataSize = pcmData.length;
    final int fileSize = 36 + dataSize;

    final List<int> wavHeader = [
      // RIFF header
      0x52, 0x49, 0x46, 0x46, // "RIFF"
      fileSize & 0xff, (fileSize >> 8) & 0xff, (fileSize >> 16) & 0xff,
      (fileSize >> 24) & 0xff,
      0x57, 0x41, 0x56, 0x45, // "WAVE"

      // fmt chunk
      0x66, 0x6d, 0x74, 0x20, // "fmt "
      16, 0, 0, 0, // chunk size
      1, 0, // audio format (PCM)
      channels, 0, // number of channels
      sampleRate & 0xff, (sampleRate >> 8) & 0xff, (sampleRate >> 16) & 0xff,
      (sampleRate >> 24) & 0xff,
      (sampleRate * channels * bitsPerSample ~/ 8) & 0xff,
      ((sampleRate * channels * bitsPerSample ~/ 8) >> 8) & 0xff,
      ((sampleRate * channels * bitsPerSample ~/ 8) >> 16) & 0xff,
      ((sampleRate * channels * bitsPerSample ~/ 8) >> 24) & 0xff,
      (channels * bitsPerSample ~/ 8), 0, // block align
      bitsPerSample, 0, // bits per sample

      // data chunk
      0x64, 0x61, 0x74, 0x61, // "data"
      dataSize & 0xff, (dataSize >> 8) & 0xff, (dataSize >> 16) & 0xff,
      (dataSize >> 24) & 0xff,
    ];

    return Uint8List.fromList([...wavHeader, ...pcmData]);
  }

  /// إغلاق الاتصال
  static void disconnect() {
    try {
      // إيقاف المحادثة الصوتية والتسجيل
      disableVoiceMode();

      _channel?.sink.close();
      _channel = null;
      _isConnected = false;

      // إيقاف وتنظيف مشغل الصوت
      _audioPlayer?.stop();
      _audioPlayer?.dispose();
      _audioPlayer = null;
      _audioBuffer.clear();

      log("🔴 Realtime API disconnected");
    } catch (e) {
      log("❌ Error disconnecting: $e");
    }
  }

  // Getters
  static bool get isConnected => _isConnected;
  static bool get isVoiceModeEnabled => _voiceModeEnabled;
  static WebSocketChannel? get channel => _channel;
}
